# 海报生成服务 - 自定义路径功能使用说明

## 功能概述

海报生成服务现在支持Java端自定义存储路径，允许灵活指定生成的海报文件保存位置。

## 主要特性

- ✅ 支持Java端传递自定义存储路径
- ✅ 自动创建不存在的目录
- ✅ 向后兼容，不传路径时使用默认配置
- ✅ 返回完整路径信息供调用方使用

## API 使用方法

### 1. Node.js 服务直接调用

**接口地址**: `POST http://localhost:3001/api/generate-poster`

**请求参数**:
```json
{
  "url": "http://localhost:8080/#/pages/case/poster?caseId=1",
  "caseId": "1",
  "width": 1200,
  "height": 1600,
  "uploadPath": "/Users/<USER>/uploadPath/custom-poster"  // 可选，自定义路径
}
```

**响应数据**:
```json
{
  "success": true,
  "message": "海报生成成功",
  "data": {
    "fileName": "poster_case_1_1703123456789.png",
    "filePath": "/Users/<USER>/uploadPath/custom-poster/poster_case_1_1703123456789.png",
    "relativePath": "/poster/poster_case_1_1703123456789.png",
    "uploadDir": "/Users/<USER>/uploadPath/custom-poster",
    "caseId": "1"
  }
}
```

### 2. Java 服务调用

**接口地址**: `POST http://localhost:8080/system/caseInfo/mobile/poster/{caseId}`

**使用自定义路径**:
```bash
curl -X POST "http://localhost:8080/system/caseInfo/mobile/poster/1?uploadPath=/custom/path" \
  -H "Content-Type: application/json"
```

**使用默认路径**:
```bash
curl -X POST "http://localhost:8080/system/caseInfo/mobile/poster/1" \
  -H "Content-Type: application/json"
```

**Java代码示例**:
```java
// 使用默认路径
String posterUrl = caseInfoService.generateCasePoster(caseId, request);

// 使用自定义路径
String customPath = "/Users/<USER>/uploadPath/custom-poster";
String posterUrl = caseInfoService.generateCasePoster(caseId, request, customPath);
```

## 配置说明

### 环境变量配置

可以通过环境变量配置默认存储路径：

```bash
export UPLOAD_DIR="/Users/<USER>/uploadPath/poster"
```

### 路径优先级

1. **最高优先级**: Java端传递的 `customUploadPath` 参数
2. **中等优先级**: Node.js请求中的 `uploadPath` 参数  
3. **默认优先级**: 环境变量 `UPLOAD_DIR`
4. **兜底优先级**: 硬编码默认路径 `/Users/<USER>/uploadPath/poster`

## 文件命名规则

生成的海报文件命名格式：`poster_case_{caseId}_{timestamp}.png`

示例：`poster_case_1_1703123456789.png`

## 目录自动创建

服务会自动创建不存在的目录，无需手动创建。

## 测试方法

1. 运行测试脚本：
```bash
./test-custom-path-poster.sh
```

2. 检查服务状态：
```bash
curl http://localhost:3001/health
```

3. 查看服务信息：
```bash
curl http://localhost:3001/api/info
```

## 注意事项

1. **权限要求**: 确保服务有权限在指定路径创建目录和文件
2. **路径格式**: 使用绝对路径，避免相对路径可能导致的问题
3. **磁盘空间**: 确保目标路径有足够的磁盘空间
4. **路径安全**: 避免使用系统敏感目录

## 错误处理

- 如果自定义路径无法创建或写入，服务会返回错误信息
- 建议在调用前检查路径的有效性和权限
- 可以通过日志查看详细的错误信息

## 版本信息

- **当前版本**: 1.1.0
- **新增功能**: 自定义存储路径支持
- **兼容性**: 向后兼容，现有调用方式不受影响
