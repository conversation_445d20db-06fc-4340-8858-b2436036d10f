package com.ruoyi.system.service;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * 海报生成服务
 * 
 * <AUTHOR>
 */
@Service
public class PosterGenerationService {
    
    private static final Logger logger = LoggerFactory.getLogger(PosterGenerationService.class);
    

    /**
     * 生成案例海报（支持自定义存储路径）
     *
     * @param caseId 案例ID
     * @param request HTTP请求对象
     * @param customUploadPath 自定义存储路径，为null时使用默认路径
     * @return 海报图片URL
     * @throws Exception 生成异常
     */
    public String generateCasePoster(Long caseId, HttpServletRequest request, String customUploadPath) throws Exception {
        try {
            logger.info("开始生成案例海报 - 案例ID: {}, 自定义路径: {}", caseId, customUploadPath);

            // 构建海报页面URL
            String baseUrl = "http://localhost:8080";
            String posterPageUrl = baseUrl + "/#/pages/case/poster?caseId=" + caseId;
            logger.info("海报页面URL: {}", posterPageUrl);

            // 尝试调用Node.js海报生成服务
            try {
                String nodeServiceUrl = "http://localhost:3001/api/generate-poster";
                String posterUrl = callPosterService(nodeServiceUrl, posterPageUrl, caseId, customUploadPath);
                if (posterUrl != null) {
                    logger.info("通过Node.js服务生成海报成功 - 案例ID: {}, URL: {}", caseId, posterUrl);
                    return posterUrl;
                }
            } catch (Exception e) {
                logger.warn("Node.js海报服务调用失败，使用备用方案: {}", e.getMessage());
            }
           return null;
        } catch (Exception e) {
            logger.error("生成案例海报失败 - 案例ID: {}", caseId, e);
            throw new Exception("海报生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 调用Node.js海报生成服务（支持自定义路径）
     */
    private String callPosterService(String serviceUrl, String pageUrl, Long caseId, String customUploadPath) throws Exception {
        logger.info("尝试调用海报生成服务: {}, 自定义路径: {}", serviceUrl, customUploadPath);

        try {
            // 构建请求参数
            String requestBody;
            if (customUploadPath != null && !customUploadPath.trim().isEmpty()) {
                requestBody = String.format(
                    "{\"url\":\"%s\",\"caseId\":\"%s\",\"width\":1200,\"height\":1600,\"uploadPath\":\"%s\"}",
                    pageUrl, caseId, customUploadPath
                );
            } else {
                requestBody = String.format(
                    "{\"url\":\"%s\",\"caseId\":\"%s\",\"width\":1200,\"height\":1600}",
                    pageUrl, caseId
                );
            }

            // 使用URLConnection发送HTTP请求
            URL url = new URL(serviceUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setDoOutput(true);
            connection.setConnectTimeout(30000);
            connection.setReadTimeout(30000);

            // 发送请求体
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = requestBody.getBytes("utf-8");
                os.write(input, 0, input.length);
            }

            // 读取响应
            int responseCode = connection.getResponseCode();
            StringBuilder responseBody = new StringBuilder();

            try (BufferedReader br = new BufferedReader(
                new InputStreamReader(connection.getInputStream(), "utf-8"))) {
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    responseBody.append(responseLine.trim());
                }
            }

            if (responseCode == 200) {
                // 解析响应JSON
                String responseBodyStr = responseBody.toString();
                logger.info("海报服务响应: {}", responseBodyStr);

                // 尝试解析relativePath字段（向后兼容）
                if (responseBodyStr.contains("\"relativePath\":")) {
                    int start = responseBodyStr.indexOf("\"relativePath\":\"") + 16;
                    int end = responseBodyStr.indexOf("\"", start);
                    if (start > 15 && end > start) {
                        String relativePath = responseBodyStr.substring(start, end);
                        logger.info("海报生成成功，相对路径: {}", relativePath);
                        return relativePath;
                    }
                }
            }
            logger.warn("海报服务调用失败，状态码: {}, 响应: {}", responseCode, responseBody.toString());
            return null;
        } catch (Exception e) {
            logger.error("调用海报生成服务异常: {}", e.getMessage());
            throw e;
        }
    }
    


}
