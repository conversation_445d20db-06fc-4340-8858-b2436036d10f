package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.CaseInfo;

/**
 * 案例信息Service接口
 * 
 * <AUTHOR>
 */
public interface ICaseInfoService 
{
    /**
     * 查询案例信息
     * 
     * @param caseId 案例信息主键
     * @return 案例信息
     */
    public CaseInfo selectCaseInfoByCaseId(Long caseId);

    /**
     * 查询案例信息列表
     * 
     * @param caseInfo 案例信息
     * @return 案例信息集合
     */
    public List<CaseInfo> selectCaseInfoList(CaseInfo caseInfo);

    /**
     * 查询推荐案例列表（用于首页展示）
     * 
     * @return 案例信息集合
     */
    public List<CaseInfo> selectRecommendedCases();

    /**
     * 查询最新案例列表（用于首页展示）
     * 
     * @return 案例信息集合
     */
    public List<CaseInfo> selectLatestCases();

    /**
     * 根据发布人ID查询案例列表
     * 
     * @param publisherId 发布人ID
     * @return 案例信息集合
     */
    public List<CaseInfo> selectCaseInfoByPublisherId(Long publisherId);

    /**
     * 根据标签查询案例列表
     * 
     * @param tag 标签
     * @return 案例信息集合
     */
    public List<CaseInfo> selectCaseInfoByTag(String tag);

    /**
     * 新增案例信息
     * 
     * @param caseInfo 案例信息
     * @return 结果
     */
    public int insertCaseInfo(CaseInfo caseInfo);

    /**
     * 修改案例信息
     * 
     * @param caseInfo 案例信息
     * @return 结果
     */
    public int updateCaseInfo(CaseInfo caseInfo);

    /**
     * 增加点击次数
     * 
     * @param caseId 案例ID
     * @return 结果
     */
    public int incrementClickCount(Long caseId);

    /**
     * 批量删除案例信息
     * 
     * @param caseIds 需要删除的案例信息主键集合
     * @return 结果
     */
    public int deleteCaseInfoByCaseIds(Long[] caseIds);

    /**
     * 删除案例信息信息
     * 
     * @param caseId 案例信息主键
     * @return 结果
     */
    public int deleteCaseInfoByCaseId(Long caseId);

    /**
     * 校验案例标题是否唯一
     * 
     * @param caseInfo 案例信息
     * @return 结果
     */
    public boolean checkCaseTitleUnique(CaseInfo caseInfo);

    /**
     * 导入案例信息数据
     *
     * @param caseInfoList 案例信息数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importCaseInfo(List<CaseInfo> caseInfoList, Boolean isUpdateSupport, String operName);


    /**
     * 生成案例海报（支持自定义存储路径）
     *
     * @param caseId 案例ID
     * @param request HTTP请求对象
     * @param customUploadPath 自定义存储路径，为null时使用默认路径
     * @return 海报图片URL
     */
    public String generateCasePoster(Long caseId, javax.servlet.http.HttpServletRequest request, String customUploadPath) throws Exception;
}
