const express = require("express");
const puppeteer = require("puppeteer");
const cors = require("cors");
const fs = require("fs");
const path = require("path");

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(express.json());

// 配置上传目录
const UPLOAD_DIR = process.env.UPLOAD_DIR || "/Users/<USER>/uploadPath/poster";

// 确保上传目录存在
if (!fs.existsSync(UPLOAD_DIR)) {
  fs.mkdirSync(UPLOAD_DIR, { recursive: true });
}

/**
 * 生成海报接口
 *
 * 请求参数:
 * - url: 海报页面URL (必需)
 * - caseId: 案例ID (必需)
 * - width: 截图宽度 (可选，默认1200)
 * - height: 截图高度 (可选，默认1600)
 * - uploadPath: 自定义存储路径 (可选，不传则使用默认路径)
 *
 * 返回数据:
 * - fileName: 生成的文件名
 * - filePath: 完整文件路径
 * - relativePath: 相对路径（供前端使用）
 * - uploadDir: 实际使用的存储目录
 * - caseId: 案例ID
 */
app.post("/api/generate-poster", async (req, res) => {
  let browser = null;

  try {
    const { url, caseId, width = 1200, height = 1600, uploadPath } = req.body;

    if (!url || !caseId) {
      return res.status(400).json({
        success: false,
        message: "缺少必要参数: url 和 caseId",
      });
    }

    // 确定存储目录：优先使用传入的uploadPath，否则使用默认配置
    const targetUploadDir = uploadPath || UPLOAD_DIR;

    // 确保目标上传目录存在
    if (!fs.existsSync(targetUploadDir)) {
      fs.mkdirSync(targetUploadDir, { recursive: true });
      console.log(`创建上传目录: ${targetUploadDir}`);
    }

    console.log(
      `开始生成海报 - 案例ID: ${caseId}, URL: ${url}, 存储路径: ${targetUploadDir}`
    );

    // 启动浏览器
    browser = await puppeteer.launch({
      headless: true,
      args: [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        "--disable-gpu",
        "--disable-web-security",
        "--allow-running-insecure-content",
        "--disable-background-timer-throttling",
        "--disable-backgrounding-occluded-windows",
        "--disable-renderer-backgrounding",
      ],
      timeout: 30000,
      protocolTimeout: 30000,
    });

    const page = await browser.newPage();

    // 设置视口大小
    await page.setViewport({
      width: parseInt(width),
      height: parseInt(height),
      deviceScaleFactor: 2, // 高清截图
    });

    // 设置用户代理
    await page.setUserAgent(
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    );

    // 访问页面
    console.log(`正在访问页面: ${url}`);
    await page.goto(url, {
      waitUntil: "networkidle2",
      timeout: 30000,
    });

    // 等待页面内容加载完成
    try {
      await page.waitForSelector(".case-title", { timeout: 10000 });
      console.log("页面内容加载完成");
    } catch (error) {
      console.warn("等待页面内容超时，继续截图");
    }

    // 额外等待确保二维码等动态内容生成完成
    await page.waitForTimeout(3000);

    // 生成文件名
    const timestamp = Date.now();
    const fileName = `poster_case_${caseId}_${timestamp}.png`;
    const filePath = path.join(targetUploadDir, fileName);

    // 截取整个页面
    console.log("开始截取页面截图...");
    await page.screenshot({
      path: filePath,
      fullPage: true,
      type: "png",
    });

    console.log(`海报生成成功: ${filePath}`);

    // 返回结果
    res.json({
      success: true,
      message: "海报生成成功",
      data: {
        fileName: fileName,
        filePath: filePath, // 返回完整的文件路径
        relativePath: `/poster/${fileName}`, // 保留相对路径供前端使用
        uploadDir: targetUploadDir, // 返回实际使用的存储目录
        caseId: caseId,
      },
    });
  } catch (error) {
    console.error("生成海报失败:", error);
    res.status(500).json({
      success: false,
      message: "海报生成失败: " + error.message,
    });
  } finally {
    // 关闭浏览器
    if (browser) {
      try {
        await browser.close();
      } catch (error) {
        console.warn("关闭浏览器失败:", error.message);
      }
    }
  }
});

/**
 * 健康检查接口
 */
app.get("/health", (req, res) => {
  res.json({
    success: true,
    message: "海报生成服务运行正常",
    timestamp: new Date().toISOString(),
  });
});

/**
 * 获取服务信息
 */
app.get("/api/info", (req, res) => {
  res.json({
    name: "Case Poster Service",
    version: "1.1.0",
    description: "案例海报生成服务（支持自定义存储路径）",
    features: [
      "支持自定义存储路径",
      "自动创建目录",
      "高清截图生成",
      "完整页面截取",
    ],
    endpoints: {
      "POST /api/generate-poster": {
        description: "生成海报",
        parameters: {
          url: "海报页面URL (必需)",
          caseId: "案例ID (必需)",
          width: "截图宽度 (可选，默认1200)",
          height: "截图高度 (可选，默认1600)",
          uploadPath: "自定义存储路径 (可选)",
        },
        response: {
          fileName: "生成的文件名",
          filePath: "完整文件路径",
          relativePath: "相对路径",
          uploadDir: "实际存储目录",
          caseId: "案例ID",
        },
      },
      "GET /health": "健康检查",
      "GET /api/info": "服务信息",
    },
    defaultUploadDir: UPLOAD_DIR,
  });
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error("服务器错误:", error);
  res.status(500).json({
    success: false,
    message: "服务器内部错误",
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: "接口不存在",
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`海报生成服务已启动，端口: ${PORT}`);
  console.log(`上传目录: ${UPLOAD_DIR}`);
  console.log(`健康检查: http://localhost:${PORT}/health`);
  console.log(`服务信息: http://localhost:${PORT}/api/info`);
});

// 优雅关闭
process.on("SIGTERM", () => {
  console.log("收到SIGTERM信号，正在关闭服务...");
  process.exit(0);
});

process.on("SIGINT", () => {
  console.log("收到SIGINT信号，正在关闭服务...");
  process.exit(0);
});
