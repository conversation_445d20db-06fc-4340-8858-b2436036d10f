/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
import type { <PERSON>rows<PERSON> } from '../api/Browser.js';
import type { ConnectOptions } from './ConnectOptions.js';
/**
 * Users should never call this directly; it's called when calling
 * `puppeteer.connect`. This method attaches P<PERSON>peteer to an existing browser instance.
 *
 * @internal
 */
export declare function _connectToBrowser(options: ConnectOptions): Promise<Browser>;
//# sourceMappingURL=BrowserConnector.d.ts.map