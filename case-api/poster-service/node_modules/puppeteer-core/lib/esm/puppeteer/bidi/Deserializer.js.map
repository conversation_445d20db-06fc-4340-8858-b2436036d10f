{"version": 3, "file": "Deserializer.js", "sourceRoot": "", "sources": ["../../../../src/bidi/Deserializer.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAIH,OAAO,EAAC,UAAU,EAAC,MAAM,mBAAmB,CAAC;AAE7C;;GAEG;AACH,MAAM,OAAO,gBAAgB;IAC3B,MAAM,CAAC,iBAAiB,CAAC,KAAyC;QAChE,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,IAAI;gBACP,OAAO,CAAC,CAAC,CAAC;YACZ,KAAK,KAAK;gBACR,OAAO,GAAG,CAAC;YACb,KAAK,UAAU;gBACb,OAAO,QAAQ,CAAC;YAClB,KAAK,WAAW;gBACd,OAAO,CAAC,QAAQ,CAAC;YACnB;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAED,MAAM,CAAC,qBAAqB,CAAC,MAA+B;QAC1D,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;YACpB,KAAK,OAAO;gBACV,OAAO,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE;oBAC/B,OAAO,gBAAgB,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;gBACvD,CAAC,CAAC,CAAC;YACL,KAAK,KAAK;gBACR,OAAO,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,GAAiB,EAAE,KAAK,EAAE,EAAE;oBACvD,OAAO,GAAG,CAAC,GAAG,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,CAAC;gBAChE,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;YAChB,KAAK,QAAQ;gBACX,OAAO,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,GAAyB,EAAE,KAAK,EAAE,EAAE;oBAC/D,MAAM,EAAC,GAAG,EAAE,KAAK,EAAC,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;oBAC9D,GAAG,CAAC,GAAU,CAAC,GAAG,KAAK,CAAC;oBACxB,OAAO,GAAG,CAAC;gBACb,CAAC,EAAE,EAAE,CAAC,CAAC;YACT,KAAK,KAAK;gBACR,OAAO,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,GAA0B,EAAE,KAAK,EAAE,EAAE;oBAChE,MAAM,EAAC,GAAG,EAAE,KAAK,EAAC,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;oBAC9D,OAAO,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC7B,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;YAChB,KAAK,SAAS;gBACZ,OAAO,EAAE,CAAC;YACZ,KAAK,QAAQ;gBACX,OAAO,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC9D,KAAK,MAAM;gBACT,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAChC,KAAK,WAAW;gBACd,OAAO,SAAS,CAAC;YACnB,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC;YACd,KAAK,QAAQ;gBACX,OAAO,gBAAgB,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC1D,KAAK,QAAQ;gBACX,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9B,KAAK,SAAS;gBACZ,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC/B,KAAK,QAAQ;gBACX,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,CAAC;QAED,UAAU,CAAC,2BAA2B,MAAM,CAAC,IAAI,iBAAiB,CAAC,CAAC;QACpE,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM,CAAC,gBAAgB,CAAC,CAAC,aAAa,EAAE,eAAe,CAGtD;QACC,MAAM,GAAG,GACP,OAAO,aAAa,KAAK,QAAQ;YAC/B,CAAC,CAAC,aAAa;YACf,CAAC,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;QAC5D,MAAM,KAAK,GAAG,gBAAgB,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAC;QAEtE,OAAO,EAAC,GAAG,EAAE,KAAK,EAAC,CAAC;IACtB,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,MAA+B;QAChD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,UAAU,CAAC,mCAAmC,CAAC,CAAC;YAChD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,gBAAgB,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;IACxD,CAAC;CACF"}