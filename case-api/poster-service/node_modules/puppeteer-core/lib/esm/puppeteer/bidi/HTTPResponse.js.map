{"version": 3, "file": "HTTPResponse.js", "sourceRoot": "", "sources": ["../../../../src/bidi/HTTPResponse.ts"], "names": [], "mappings": "AASA,OAAO,EACL,YAAY,IAAI,YAAY,GAE7B,MAAM,wBAAwB,CAAC;AAChC,OAAO,EAAC,oBAAoB,EAAC,MAAM,qBAAqB,CAAC;AAIzD;;GAEG;AACH,MAAM,OAAO,gBAAiB,SAAQ,YAAY;IAChD,QAAQ,CAAkB;IAC1B,cAAc,CAAgB;IAC9B,OAAO,CAAS;IAChB,WAAW,CAAS;IACpB,IAAI,CAAS;IACb,UAAU,CAAU;IACpB,QAAQ,GAA2B,EAAE,CAAC;IACtC,QAAQ,CAAgC;IAExC,YACE,OAAwB,EACxB,EAAC,QAAQ,EAA2C;QAEpD,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,CAAC,cAAc,GAAG;YACpB,EAAE,EAAE,EAAE;YACN,IAAI,EAAE,CAAC,CAAC;SACT,CAAC;QAEF,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,SAAS,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,UAAU,CAAC;QACvC,sCAAsC;QACtC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,+HAA+H;QAC/H,KAAK,MAAM,MAAM,IAAI,QAAQ,CAAC,OAAO,IAAI,EAAE,EAAE,CAAC;YAC5C,qCAAqC;YACrC,4DAA4D;YAC5D,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACnC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YAChE,CAAC;QACH,CAAC;IACH,CAAC;IAEQ,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAEQ,GAAG;QACV,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAEQ,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAEQ,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAEQ,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAEQ,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAEQ,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAEQ,MAAM;QACb,OAAO,IAAI,CAAC,QAAe,CAAC;IAC9B,CAAC;IAEQ,KAAK;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;IAEQ,iBAAiB;QACxB,OAAO,KAAK,CAAC;IACf,CAAC;IAEQ,eAAe;QACtB,MAAM,IAAI,oBAAoB,EAAE,CAAC;IACnC,CAAC;IAEQ,MAAM;QACb,MAAM,IAAI,oBAAoB,EAAE,CAAC;IACnC,CAAC;CACF"}