{"version": 3, "file": "FirefoxTargetManager.js", "sourceRoot": "", "sources": ["../../../../src/cdp/FirefoxTargetManager.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAKH,OAAO,EAAkB,eAAe,EAAC,MAAM,sBAAsB,CAAC;AACtE,OAAO,EAAC,YAAY,EAAC,MAAM,2BAA2B,CAAC;AACvD,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAC,QAAQ,EAAC,MAAM,qBAAqB,CAAC;AAY7C;;;;;;;;;;;;;GAaG;AACH,MAAM,OAAO,oBACX,SAAQ,YAAiC;IAGzC,WAAW,CAAa;IACxB;;;;;;;;;OASG;IACH,4BAA4B,GAAG,IAAI,GAAG,EAAsC,CAAC;IAC7E;;;;;OAKG;IACH,2BAA2B,GAAG,IAAI,GAAG,EAAqB,CAAC;IAC3D;;OAEG;IACH,4BAA4B,GAAG,IAAI,GAAG,EAAqB,CAAC;IAC5D,qBAAqB,CAAmC;IACxD,cAAc,CAAgB;IAE9B,mCAAmC,GAAG,IAAI,OAAO,EAG9C,CAAC;IAEJ,mBAAmB,GAAG,QAAQ,CAAC,MAAM,EAAQ,CAAC;IAC9C,kBAAkB,GAAG,IAAI,GAAG,EAAU,CAAC;IAEvC,YACE,UAAsB,EACtB,aAA4B,EAC5B,oBAA2C;QAE3C,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,qBAAqB,GAAG,oBAAoB,CAAC;QAClD,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QAEpC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,sBAAsB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACnE,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,wBAAwB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACvE,IAAI,CAAC,WAAW,CAAC,EAAE,CACjB,eAAe,CAAC,eAAe,EAC/B,IAAI,CAAC,kBAAkB,CACxB,CAAC;QACF,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;IAED,wBAAwB,CAAC,OAAgC;QACvD,MAAM,QAAQ,GAAG,CAAC,KAA4C,EAAE,EAAE;YAChE,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC;QACF,MAAM,CAAC,CAAC,IAAI,CAAC,mCAAmC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,mCAAmC,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAChE,OAAO,CAAC,EAAE,CAAC,yBAAyB,EAAE,QAAQ,CAAC,CAAC;IAClD,CAAC;IAED,kBAAkB,GAAG,CAAC,OAAmB,EAAE,EAAE;QAC3C,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QACrC,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;IACzD,CAAC,CAAC;IAEF,sBAAsB,CAAC,OAAmB;QACxC,IAAI,IAAI,CAAC,mCAAmC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YAC1D,OAAO,CAAC,GAAG,CACT,yBAAyB,EACzB,IAAI,CAAC,mCAAmC,CAAC,GAAG,CAAC,OAAO,CAAE,CACvD,CAAC;YACF,IAAI,CAAC,mCAAmC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAED,mBAAmB;QACjB,OAAO,IAAI,CAAC,2BAA2B,CAAC;IAC1C,CAAC;IAED,OAAO;QACL,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACpE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAC1E,CAAC;IAED,KAAK,CAAC,UAAU;QACd,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,2BAA2B,EAAE;YACvD,QAAQ,EAAE,IAAI;YACd,MAAM,EAAE,CAAC,EAAE,CAAC;SACb,CAAC,CAAC;QACH,IAAI,CAAC,kBAAkB,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,4BAA4B,CAAC,IAAI,EAAE,CAAC,CAAC;QAC5E,MAAM,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,CAAC;IAChD,CAAC;IAED,gBAAgB,GAAG,KAAK,EACtB,KAAyC,EAC1B,EAAE;QACjB,IAAI,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrE,OAAO;QACT,CAAC;QAED,IAAI,CAAC,4BAA4B,CAAC,GAAG,CACnC,KAAK,CAAC,UAAU,CAAC,QAAQ,EACzB,KAAK,CAAC,UAAU,CACjB,CAAC;QAEF,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;YACrE,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YAChE,MAAM,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACxE,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACpD,OAAO;QACT,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAChE,IAAI,IAAI,CAAC,qBAAqB,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,CAAC;YACtE,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAC7D,OAAO;QACT,CAAC;QACD,MAAM,CAAC,WAAW,EAAE,CAAC;QACrB,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACxE,IAAI,CAAC,IAAI,6DAAqC,MAAM,CAAC,CAAC;QACtD,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACtD,CAAC,CAAC;IAEF,kBAAkB,GAAG,CAAC,KAA2C,EAAQ,EAAE;QACzE,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACzD,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpE,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,IAAI,mDAAgC,MAAM,CAAC,CAAC;YACjD,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC,CAAC;IAEF,mBAAmB,GAAG,KAAK,EACzB,aAAsC,EACtC,KAA4C,EAC5C,EAAE;QACF,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;QACpC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAC1D,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,WAAW,KAAK,CAAC,SAAS,mBAAmB,CAAC,CAAC;QACjE,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAEzE,MAAM,CAAC,MAAM,EAAE,UAAU,UAAU,CAAC,QAAQ,aAAa,CAAC,CAAC;QAE1D,OAAyB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAC9C,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAEvC,IAAI,CAAC,4BAA4B,CAAC,GAAG,CACnC,OAAO,CAAC,EAAE,EAAE,EACZ,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAE,CAC3D,CAAC;QAEF,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IACrD,CAAC,CAAC;IAEF,4BAA4B,CAAC,QAAgB;QAC3C,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACzC,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;QACrC,CAAC;IACH,CAAC;CACF"}