/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
import type { ConnectionTransport } from '../common/ConnectionTransport.js';
import type { BrowserConnectOptions, ConnectOptions } from '../common/ConnectOptions.js';
import { CdpBrowser } from './Browser.js';
/**
 * Users should never call this directly; it's called when calling
 * `puppeteer.connect` with `protocol: 'cdp'`.
 *
 * @internal
 */
export declare function _connectToCdpBrowser(connectionTransport: ConnectionTransport, url: string, options: BrowserConnectOptions & ConnectOptions): Promise<CdpBrowser>;
//# sourceMappingURL=BrowserConnector.d.ts.map