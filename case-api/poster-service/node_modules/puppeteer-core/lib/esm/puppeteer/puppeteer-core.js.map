{"version": 3, "file": "puppeteer-core.js", "sourceRoot": "", "sources": ["../../../src/puppeteer-core.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAIH,cAAc,cAAc,CAAC;AAC7B,cAAc,cAAc,CAAC;AAC7B,cAAc,oBAAoB,CAAC;AACnC,cAAc,gBAAgB,CAAC;AAC/B,cAAc,gBAAgB,CAAC;AAC/B,cAAc,gBAAgB,CAAC;AAE/B;;GAEG;AACH,cAAc,gCAAgC,CAAC;AAE/C,OAAO,EAAC,aAAa,EAAC,MAAM,yBAAyB,CAAC;AAEtD;;GAEG;AACH,MAAM,SAAS,GAAG,IAAI,aAAa,CAAC;IAClC,eAAe,EAAE,IAAI;CACtB,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM;AACX;;GAEG;AACH,OAAO;AACP;;GAEG;AACH,WAAW;AACX;;GAEG;AACH,cAAc;AACd;;GAEG;AACH,MAAM,GACP,GAAG,SAAS,CAAC;AAEd,eAAe,SAAS,CAAC"}