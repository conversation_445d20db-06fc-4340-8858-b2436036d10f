{"version": 3, "file": "getConfiguration.js", "sourceRoot": "", "sources": ["../../../src/getConfiguration.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,OAAO,EAAC,MAAM,IAAI,CAAC;AAC3B,OAAO,EAAC,IAAI,EAAC,MAAM,MAAM,CAAC;AAE1B,OAAO,EAAC,eAAe,EAAC,MAAM,aAAa,CAAC;AAG5C,SAAS,gBAAgB,CAAC,IAAY;IACpC,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC9B,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;QACtB,OAAO;IACT,CAAC;IACD,QAAQ,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC;QAC1B,KAAK,EAAE,CAAC;QACR,KAAK,GAAG,CAAC;QACT,KAAK,OAAO,CAAC;QACb,KAAK,KAAK;YACR,OAAO,KAAK,CAAC;QACf;YACE,OAAO,IAAI,CAAC;IAChB,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,kBAAkB,CAAC,OAAgB;IAC1C,QAAQ,OAAO,EAAE,CAAC;QAChB,KAAK,QAAQ,CAAC;QACd,KAAK,SAAS;YACZ,OAAO,IAAI,CAAC;QACd;YACE,OAAO,KAAK,CAAC;IACjB,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,GAAkB,EAAE;IAClD,MAAM,MAAM,GAAG,eAAe,CAAC,WAAW,EAAE;QAC1C,cAAc,EAAE,QAAQ;KACzB,CAAC,CAAC,MAAM,EAAE,CAAC;IACZ,MAAM,aAAa,GAAkB,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;IAEjE,aAAa,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC;QACzD,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC;QAClC,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC;QAC1C,aAAa,CAAC,QAAQ;QACtB,MAAM,CAAgC,CAAC;IAEzC,iCAAiC;IACjC,aAAa,CAAC,cAAc,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC;QAC9D,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC;QACnD,aAAa,CAAC,cAAc;QAC5B,QAAQ,CAAY,CAAC;IAEvB,aAAa,CAAC,cAAc;QAC1B,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC;YACnD,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC;YAC3D,aAAa,CAAC,cAAc,CAAC;IAE/B,mDAAmD;IACnD,IAAI,aAAa,CAAC,cAAc,EAAE,CAAC;QACjC,aAAa,CAAC,YAAY,GAAG,IAAI,CAAC;IACpC,CAAC;IAED,8CAA8C;IAC9C,aAAa,CAAC,YAAY,GAAG,OAAO,CAClC,gBAAgB,CAAC,yBAAyB,CAAC;QACzC,gBAAgB,CAAC,oCAAoC,CAAC;QACtD,gBAAgB,CAAC,4CAA4C,CAAC;QAC9D,aAAa,CAAC,YAAY,CAC7B,CAAC;IAEF,oDAAoD;IACpD,aAAa,CAAC,kBAAkB,GAAG,OAAO,CACxC,gBAAgB,CAAC,gCAAgC,CAAC;QAChD,gBAAgB,CAAC,2CAA2C,CAAC;QAC7D,gBAAgB,CAAC,mDAAmD,CAAC;QACrE,aAAa,CAAC,kBAAkB,CACnC,CAAC;IAEF,oDAAoD;IACpD,aAAa,CAAC,+BAA+B,GAAG,OAAO,CACrD,gBAAgB,CAAC,+CAA+C,CAAC;QAC/D,gBAAgB,CACd,0DAA0D,CAC3D;QACD,gBAAgB,CACd,kEAAkE,CACnE;QACD,aAAa,CAAC,+BAA+B,CAChD,CAAC;IAEF,gDAAgD;IAChD,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;QAChC,aAAa,CAAC,eAAe;YAC3B,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC;gBACzC,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC;gBACpD,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC;gBAC5D,aAAa,CAAC,eAAe,CAAC;QAEhC,MAAM,YAAY,GAChB,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC;YACjD,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAE5D,IAAI,YAAY,IAAI,aAAa,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;YACtD,OAAO,CAAC,IAAI,CACV,iFAAiF,CAClF,CAAC;QACJ,CAAC;QAED,aAAa,CAAC,eAAe;YAC3B,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC;gBAC1C,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC;gBACrD,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC;gBAC7D,aAAa,CAAC,eAAe;gBAC7B,YAAY,CAAC;QAEf,aAAa,CAAC,YAAY;YACxB,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC;gBACtC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC;gBACjD,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC;gBACzD,aAAa,CAAC,YAAY,CAAC;IAC/B,CAAC;IAED,aAAa,CAAC,cAAc;QAC1B,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC;YAClC,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC;YAC7C,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC;YACrD,aAAa,CAAC,cAAc;YAC5B,IAAI,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;IACzC,aAAa,CAAC,kBAAkB;QAC9B,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC;YAChC,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC;YAC3C,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC;YACnD,aAAa,CAAC,kBAAkB,CAAC;IAEnC,aAAa,CAAC,WAAW,KAAK,EAAE,CAAC;IAEjC,0BAA0B;IAC1B,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,cAAc,CAAC,EAAE,CAAC;QACtD,MAAM,IAAI,KAAK,CAAC,uBAAuB,aAAa,CAAC,cAAc,EAAE,CAAC,CAAC;IACzE,CAAC;IAED,OAAO,aAAa,CAAC;AACvB,CAAC,CAAC"}