#!/bin/bash

echo "=== 测试自定义路径海报生成功能 ==="
echo ""

# 定义测试参数
CASE_ID="1"
CUSTOM_PATH="/Users/<USER>/uploadPath/custom-poster"
NODE_SERVICE_URL="http://localhost:3001/api/generate-poster"
JAVA_SERVICE_URL="http://localhost:8080/system/caseInfo/mobile/poster"

# 创建自定义目录
echo "1. 创建自定义存储目录..."
mkdir -p "$CUSTOM_PATH"
echo "自定义目录已创建: $CUSTOM_PATH"
echo ""

# 检查海报生成服务是否运行
echo "2. 检查海报生成服务状态..."
curl -s http://localhost:3001/health | jq . || echo "海报生成服务未运行或响应异常"
echo ""

# 测试Node.js服务直接调用（使用自定义路径）
echo "3. 测试Node.js服务直接调用（自定义路径）..."
echo "请求URL: http://localhost:8080/#/pages/case/poster?caseId=$CASE_ID"
echo "案例ID: $CASE_ID"
echo "自定义路径: $CUSTOM_PATH"
echo ""

response=$(curl -s -X POST $NODE_SERVICE_URL \
  -H "Content-Type: application/json" \
  -d "{
    \"url\": \"http://localhost:8080/#/pages/case/poster?caseId=$CASE_ID\",
    \"caseId\": \"$CASE_ID\",
    \"width\": 1200,
    \"height\": 1600,
    \"uploadPath\": \"$CUSTOM_PATH\"
  }")

echo "Node.js服务响应:"
echo "$response" | jq . || echo "$response"
echo ""

# 测试Node.js服务直接调用（使用默认路径）
echo "4. 测试Node.js服务直接调用（默认路径）..."
response_default=$(curl -s -X POST $NODE_SERVICE_URL \
  -H "Content-Type: application/json" \
  -d "{
    \"url\": \"http://localhost:8080/#/pages/case/poster?caseId=$CASE_ID\",
    \"caseId\": \"$CASE_ID\",
    \"width\": 1200,
    \"height\": 1600
  }")

echo "Node.js服务响应（默认路径）:"
echo "$response_default" | jq . || echo "$response_default"
echo ""

# 测试Java服务调用（使用自定义路径）
echo "5. 测试Java服务调用（自定义路径）..."
java_response=$(curl -s -X POST "$JAVA_SERVICE_URL/$CASE_ID?uploadPath=$CUSTOM_PATH" \
  -H "Content-Type: application/json")

echo "Java服务响应（自定义路径）:"
echo "$java_response" | jq . || echo "$java_response"
echo ""

# 测试Java服务调用（默认路径）
echo "6. 测试Java服务调用（默认路径）..."
java_response_default=$(curl -s -X POST "$JAVA_SERVICE_URL/$CASE_ID" \
  -H "Content-Type: application/json")

echo "Java服务响应（默认路径）:"
echo "$java_response_default" | jq . || echo "$java_response_default"
echo ""

# 检查生成的文件
echo "7. 检查生成的海报文件..."
echo "自定义路径文件:"
ls -la "$CUSTOM_PATH"/*.png 2>/dev/null | tail -3 || echo "自定义路径下没有找到文件"
echo ""

echo "默认路径文件:"
ls -la /Users/<USER>/uploadPath/poster/*.png 2>/dev/null | tail -3 || echo "默认路径下没有找到文件"
echo ""

echo "=== 测试完成 ==="
echo ""
echo "功能说明:"
echo "1. 支持通过uploadPath参数自定义存储路径"
echo "2. 不传递uploadPath时使用默认路径"
echo "3. Node.js服务会自动创建不存在的目录"
echo "4. Java服务通过URL参数传递自定义路径"
echo ""
echo "API使用方法:"
echo "1. Node.js直接调用: POST $NODE_SERVICE_URL"
echo "   请求体包含: {\"url\": \"...\", \"caseId\": \"...\", \"uploadPath\": \"...\"}"
echo "2. Java服务调用: POST $JAVA_SERVICE_URL/{caseId}?uploadPath=/custom/path"
echo ""
echo "返回数据格式:"
echo "- filePath: 完整文件路径"
echo "- relativePath: 相对路径（供前端使用）"
echo "- uploadDir: 实际使用的存储目录"
echo "- fileName: 生成的文件名"
