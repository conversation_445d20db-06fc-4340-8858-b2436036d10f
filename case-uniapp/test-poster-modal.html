<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海报功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            width: 100%;
            padding: 12px;
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 10px;
        }
        .btn:hover {
            background: #0056CC;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 20px;
            max-width: 300px;
            width: 90%;
            text-align: center;
        }
        .modal-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .modal-text {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        .modal-buttons {
            display: flex;
            gap: 10px;
        }
        .modal-btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }
        .modal-btn.cancel {
            background: #f0f0f0;
            color: #666;
        }
        .modal-btn.confirm {
            background: #007AFF;
            color: white;
        }
        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.8);
            z-index: 2000;
        }
        .overlay.show {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .image-container {
            text-align: center;
        }
        .poster-image {
            max-width: 100%;
            max-height: 80vh;
            border-radius: 8px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .tip {
            color: white;
            margin-top: 15px;
            opacity: 0.8;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 6px;
            text-align: center;
        }
        .status.success {
            background: #e8f5e8;
            color: #4cd964;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>海报功能测试</h2>
        <p>这个页面模拟uniapp中的海报生成功能流程</p>
        
        <button class="btn" onclick="generatePoster()">生成海报</button>
        
        <div id="status"></div>
    </div>

    <!-- 提示弹窗 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="modal-title">提示</div>
            <div class="modal-text">图片生成成功,点击确定长按图片保存</div>
            <div class="modal-buttons">
                <button class="modal-btn cancel" onclick="closeModal()">取消</button>
                <button class="modal-btn confirm" onclick="showImage()">确定</button>
            </div>
        </div>
    </div>

    <!-- 图片遮罩层 -->
    <div id="overlay" class="overlay" onclick="closeOverlay()">
        <div class="image-container" onclick="event.stopPropagation()">
            <img id="posterImage" class="poster-image" src="https://via.placeholder.com/300x400/007AFF/white?text=海报示例" alt="海报">
            <div class="tip">长按图片保存到相册</div>
        </div>
    </div>

    <script>
        function generatePoster() {
            const status = document.getElementById('status');
            status.innerHTML = '<div class="status">正在生成海报...</div>';
            
            // 模拟API调用
            setTimeout(() => {
                status.innerHTML = '<div class="status success">海报生成成功！</div>';
                document.getElementById('modal').classList.add('show');
            }, 1500);
        }

        function closeModal() {
            document.getElementById('modal').classList.remove('show');
            document.getElementById('status').innerHTML = '';
        }

        function showImage() {
            document.getElementById('modal').classList.remove('show');
            document.getElementById('overlay').classList.add('show');
        }

        function closeOverlay() {
            document.getElementById('overlay').classList.remove('show');
        }

        // 长按保存功能（模拟）
        let pressTimer;
        document.getElementById('posterImage').addEventListener('mousedown', function() {
            pressTimer = setTimeout(() => {
                alert('模拟保存图片到相册成功！');
            }, 800);
        });

        document.getElementById('posterImage').addEventListener('mouseup', function() {
            clearTimeout(pressTimer);
        });

        document.getElementById('posterImage').addEventListener('mouseleave', function() {
            clearTimeout(pressTimer);
        });

        // 移动端长按事件
        document.getElementById('posterImage').addEventListener('touchstart', function() {
            pressTimer = setTimeout(() => {
                alert('模拟保存图片到相册成功！');
            }, 800);
        });

        document.getElementById('posterImage').addEventListener('touchend', function() {
            clearTimeout(pressTimer);
        });
    </script>
</body>
</html>
