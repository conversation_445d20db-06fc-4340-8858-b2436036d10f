#!/bin/bash

echo "=== 案例海报生成功能测试 ==="
echo ""

# 检查海报生成服务是否运行
echo "1. 检查海报生成服务状态..."
curl -s http://localhost:3001/health | jq . || echo "海报生成服务未运行或响应异常"
echo ""

# 测试海报生成
echo "2. 测试海报生成功能..."
echo "请求URL: http://localhost:8080/#/pages/case/poster?caseId=1"
echo "案例ID: 1"
echo ""

response=$(curl -s -X POST http://localhost:3001/api/generate-poster \
  -H "Content-Type: application/json" \
  -d '{
    "url": "http://localhost:8080/#/pages/case/poster?caseId=1",
    "caseId": "1",
    "width": 1200,
    "height": 1600
  }')

echo "响应结果:"
echo "$response" | jq . || echo "$response"
echo ""

# 检查生成的文件
echo "3. 检查生成的海报文件..."
ls -la /Users/<USER>/uploadPath/poster/*.png | tail -5
echo ""

echo "=== 测试完成 ==="
echo ""
echo "功能说明:"
echo "1. 海报页面: http://localhost:8080/#/pages/case/poster?caseId=1"
echo "2. 海报生成API: POST http://localhost:3001/api/generate-poster"
echo "3. Java后台接口: POST /system/caseInfo/mobile/poster/{caseId}"
echo "4. 生成的海报保存在: /Users/<USER>/uploadPath/poster/"
echo ""
echo "使用方法:"
echo "1. 在案例详情页点击'生成海报'按钮"
echo "2. 系统会调用Java后台接口"
echo "3. Java后台调用Node.js海报生成服务"
echo "4. Node.js服务使用Puppeteer截取海报页面"
echo "5. 生成的海报图片保存到服务器并返回URL"
